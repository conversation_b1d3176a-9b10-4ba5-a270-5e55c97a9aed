import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from tkinterdnd2 import DND_FILES, TkinterDnD
import threading
import os
import tempfile
from PIL import Image, ImageTk, ImageGrab
import pyperclip
from paddleocr import PaddleOCR
import json
import requests
from pathlib import Path
import shutil


class OCRProcessor:
    """OCR处理器类"""

    def __init__(self):
        self.ocr = None
        self.models_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "models")
        os.makedirs(self.models_dir, exist_ok=True)
        self.is_initialized = False

    def initialize_ocr(self, callback=None):
        """异步初始化OCR引擎"""
        def init_worker():
            try:
                # 使用最基本的配置初始化OCR
                self.ocr = PaddleOCR(use_onnx=True, det_algorithm='DB', rec_algorithm='CRNN', lang='ch')
                print("OCR初始化成功")
                self.is_initialized = True
                if callback:
                    callback(True, "OCR初始化成功")
            except Exception as e:
                print(f"OCR初始化失败: {e}")
                self.is_initialized = False
                if callback:
                    callback(False, f"OCR初始化失败: {e}")

        # 在后台线程中初始化
        thread = threading.Thread(target=init_worker, daemon=True)
        thread.start()

    def load_config(self):
        """加载配置文件"""
        default_config = {
            "model_path": self.models_dir,
            "use_gpu": False,
            "language": "ch",
            "last_check": None
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, "r", encoding="utf-8") as f:
                    config = json.load(f)
                    return {**default_config, **config}
            except:
                pass
        
        # 保存默认配置
        self.save_config(default_config)
        return default_config

    def save_config(self, config):
        """保存配置文件"""
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        with open(self.config_file, "w", encoding="utf-8") as f:
            json.dump(config, f, indent=4)
        self.config = config

    def check_model_files(self):
        """检查模型文件是否存在"""
        required_files = [
            "model.pdiparams",
            "model.pdmodel",
            "config.txt"
        ]
        
        for file in required_files:
            if not os.path.exists(os.path.join(self.models_dir, file)):
                return False
        return True

    def download_model_files(self, callback=None):
        """下载模型文件"""
        def download():
            try:
                # 模型文件URL（这里使用示例URL，实际使用时需要替换为真实的URL）
                model_urls = {
                    "model.pdiparams": "https://example.com/model.pdiparams",
                    "model.pdmodel": "https://example.com/model.pdmodel",
                    "config.txt": "https://example.com/config.txt"
                }
                
                total_files = len(model_urls)
                completed_files = 0
                
                for filename, url in model_urls.items():
                    if callback:
                        callback(True, f"正在下载 {filename}...", completed_files / total_files)
                    
                    response = requests.get(url, stream=True)
                    response.raise_for_status()
                    
                    file_path = os.path.join(self.models_dir, filename)
                    with open(file_path, "wb") as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                    
                    completed_files += 1
                
                if callback:
                    callback(True, "模型文件下载完成", 1.0)
                
            except Exception as e:
                if callback:
                    callback(False, f"模型文件下载失败: {str(e)}", 0)
        
        # 在后台线程中下载
        thread = threading.Thread(target=download, daemon=True)
        thread.start()

    def process_image(self, image_path):
        """处理图片并返回识别结果"""
        if not self.is_initialized or self.ocr is None:
            raise Exception("OCR引擎未初始化，请稍候...")

        try:
            # 尝试使用新版本的predict方法
            try:
                result = self.ocr.predict(image_path)
            except AttributeError:
                # 如果没有predict方法，使用旧版本的ocr方法
                result = self.ocr.ocr(image_path, cls=True)

            if result and result[0]:
                # 提取文本内容
                texts = []
                for line in result[0]:
                    if len(line) >= 2 and len(line[1]) >= 1:
                        texts.append(line[1][0])
                return '\n'.join(texts)
            else:
                return "未识别到文字内容"
        except Exception as e:
            raise Exception(f"图片处理失败: {e}")


class OCRApp:
    """OCR UI应用程序"""

    def __init__(self):
        self.root = TkinterDnD.Tk()
        self.root.title("OCR文字识别工具 - 支持拖拽、粘贴、点击选择")
        self.root.minsize(1000, 600)

        # 初始化OCR处理器（不立即初始化）
        self.ocr_processor = OCRProcessor()

        # 当前图片路径和处理状态
        self.current_image_path = None
        self.is_processing = False
        self.ocr_ready = False

        # 图片缩放相关属性
        self.original_image = None  # 原始图片对象
        self.current_scale = 1.0    # 当前缩放比例
        self.min_scale = 0.1       # 最小缩放比例
        self.max_scale = 5.0       # 最大缩放比例
        self.scale_step = 0.1      # 每次缩放步长

        # 创建UI
        self.create_widgets()
        self.setup_drag_drop()
        self.setup_keyboard_shortcuts()

        # 异步初始化OCR
        self.start_ocr_initialization()

        # 立即尝试居中
        self.center_window()

        # 再次延迟居中，确保生效
        self.root.after(500, self.center_window)

    def center_window(self):
        """将窗口居中显示"""
        try:
            # 设置窗口大小
            window_width = 1200
            window_height = 700

            # 获取屏幕尺寸
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()

            # 计算居中位置
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2

            # 确保窗口不会超出屏幕边界
            if x < 0:
                x = 0
            if y < 0:
                y = 0

            # 使用wm_geometry方法设置位置
            self.root.wm_geometry(f"{window_width}x{window_height}+{x}+{y}")

            # 强制更新
            self.root.update_idletasks()

        except Exception as e:
            # 静默处理错误，不影响程序运行
            pass

    def start_ocr_initialization(self):
        """开始OCR初始化"""
        self.status_var.set("正在初始化OCR引擎，请稍候...")
        self.progress_bar.pack(side=tk.BOTTOM, fill=tk.X, before=self.status_bar)
        self.progress_bar.start(10)

        # 异步初始化OCR
        self.ocr_processor.initialize_ocr(self.on_ocr_initialized)

    def on_ocr_initialized(self, success, message):
        """OCR初始化完成回调"""
        def update_ui():
            self.progress_bar.stop()
            self.progress_bar.pack_forget()

            if success:
                self.ocr_ready = True
                self.status_var.set("OCR引擎初始化完成，可以开始使用")
                self.drop_area.configure(
                    text="📷 拖拽图片到此处\n📋 或按Ctrl+V粘贴\n🖱️ 或点击选择图片\n🔍 使用鼠标滚轮缩放",
                    bg="#F5F5F5",
                    fg="#666666"
                )
            else:
                self.status_var.set(f"OCR初始化失败: {message}")
                self.drop_area.configure(
                    text=f"OCR初始化失败\n{message}",
                    bg="lightcoral",
                    fg="darkred"
                )

        # 在主线程中更新UI
        self.root.after(0, update_ui)

    def show_model_download_dialog(self):
        """显示模型下载对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("下载模型文件")
        dialog.geometry("400x200")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 居中显示
        dialog.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() - dialog.winfo_width()) // 2
        y = self.root.winfo_y() + (self.root.winfo_height() - dialog.winfo_height()) // 2
        dialog.geometry(f"+{x}+{y}")
        
        # 提示文本
        ttk.Label(
            dialog,
            text="需要下载OCR模型文件才能使用文字识别功能。\n文件大小约100MB，下载可能需要几分钟时间。",
            wraplength=350,
            justify="center"
        ).pack(pady=20)
        
        # 进度条
        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(
            dialog,
            variable=progress_var,
            maximum=100,
            mode='determinate'
        )
        progress_bar.pack(fill=tk.X, padx=20, pady=10)
        
        # 状态标签
        status_var = tk.StringVar(value="准备下载...")
        status_label = ttk.Label(
            dialog,
            textvariable=status_var,
            justify="center"
        )
        status_label.pack(pady=10)
        
        def on_download_progress(success, message, progress):
            if not dialog.winfo_exists():
                return
                
            if success:
                progress_var.set(progress * 100)
                status_var.set(message)
                if progress >= 1.0:
                    dialog.destroy()
                    self.start_ocr_initialization()
            else:
                status_var.set(f"下载失败: {message}")
                progress_bar.stop()
                
                # 添加重试按钮
                ttk.Button(
                    dialog,
                    text="重试",
                    command=lambda: self.start_model_download(dialog, progress_var, status_var)
                ).pack(pady=10)
        
        # 开始下载
        self.start_model_download(dialog, progress_var, status_var)

    def start_model_download(self, dialog, progress_var, status_var):
        """开始下载模型文件"""
        # 清除可能存在的重试按钮
        for widget in dialog.winfo_children():
            if isinstance(widget, ttk.Button):
                widget.destroy()
        
        # 重置进度
        progress_var.set(0)
        status_var.set("开始下载...")
        
        # 开始下载
        self.ocr_processor.download_model_files(
            lambda s, m, p: self.root.after(0, lambda: on_download_progress(s, m, p))
        )

    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="选择图片 (Ctrl+O)", command=self.select_image)
        file_menu.add_command(label="粘贴图片 (Ctrl+V)", command=self.paste_image)
        file_menu.add_separator()
        file_menu.add_command(label="退出 (Ctrl+Q)", command=self.root.quit)

        # 编辑菜单
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="复制文本 (Ctrl+C)", command=self.copy_text)
        edit_menu.add_command(label="清空 (Ctrl+R)", command=self.clear_results)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)

    def setup_keyboard_shortcuts(self):
        """设置键盘快捷键"""
        self.root.bind('<Control-v>', lambda e: self.paste_image())
        self.root.bind('<Control-V>', lambda e: self.paste_image())
        self.root.bind('<Control-o>', lambda e: self.select_image())
        self.root.bind('<Control-O>', lambda e: self.select_image())
        self.root.bind('<Control-c>', lambda e: self.copy_text())
        self.root.bind('<Control-C>', lambda e: self.copy_text())
        self.root.bind('<Control-r>', lambda e: self.clear_results())
        self.root.bind('<Control-R>', lambda e: self.clear_results())
        self.root.bind('<Control-q>', lambda e: self.root.quit())
        self.root.bind('<Control-Q>', lambda e: self.root.quit())

        # 让窗口可以获得焦点以接收键盘事件
        self.root.focus_set()

    def create_widgets(self):
        """创建UI组件"""
        # 创建菜单栏
        self.create_menu()

        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # 左侧框架（图片区域）- 占更多空间
        left_frame = ttk.LabelFrame(main_frame, text="📷 图片区域 (支持拖拽、粘贴Ctrl+V、点击选择，鼠标滚轮缩放)", padding=15)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # 拖拽提示区域 - 改进设计
        self.drop_area = tk.Label(
            left_frame,
            text="📷 拖拽图片到此处\n📋 或按Ctrl+V粘贴\n🖱️ 或点击选择图片\n🔍 使用鼠标滚轮缩放",
            bg="#E3F2FD",
            fg="#1976D2",
            font=("Arial", 14, "bold"),
            relief=tk.GROOVE,
            bd=3,
            cursor="hand2"
        )
        self.drop_area.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.drop_area.bind("<Button-1>", self.select_image)
        
        # 绑定鼠标滚轮事件
        self.drop_area.bind("<MouseWheel>", self.on_mouse_wheel)
        
        # 创建右键菜单
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="适中显示", command=self.fit_image)
        self.context_menu.add_command(label="重置缩放", command=lambda: self.set_scale(1.0))
        
        # 绑定右键菜单
        self.drop_area.bind("<Button-3>", self.show_context_menu)
        
        # 右侧框架（结果区域）- 调整宽度比例
        right_frame = ttk.LabelFrame(main_frame, text="📝 识别结果", padding=15)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=False, padx=(10, 0))
        right_frame.configure(width=400)  # 固定宽度

        # 文本显示区域 - 改进样式
        self.text_area = scrolledtext.ScrolledText(
            right_frame,
            wrap=tk.WORD,
            font=("Microsoft YaHei", 12),  # 更好的中文字体
            height=25,
            width=45,
            bg="#FAFAFA",
            fg="#333333",
            selectbackground="#2196F3",
            selectforeground="white"
        )
        self.text_area.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 按钮框架 - 改进布局
        button_frame = ttk.Frame(right_frame)
        button_frame.pack(fill=tk.X)

        # 第一行按钮
        button_row1 = ttk.Frame(button_frame)
        button_row1.pack(fill=tk.X, pady=(0, 8))

        # 复制按钮 - 更大更明显
        self.copy_button = ttk.Button(
            button_row1,
            text="📋 复制文本 (Ctrl+C)",
            command=self.copy_text,
            state=tk.DISABLED,
            width=20
        )
        self.copy_button.pack(side=tk.LEFT, padx=(0, 8))

        # 清空按钮
        self.clear_button = ttk.Button(
            button_row1,
            text="🗑️ 清空",
            command=self.clear_results,
            width=12
        )
        self.clear_button.pack(side=tk.LEFT)

        # 第二行按钮
        button_row2 = ttk.Frame(button_frame)
        button_row2.pack(fill=tk.X)

        # 粘贴按钮
        self.paste_button = ttk.Button(
            button_row2,
            text="📋 粘贴图片 (Ctrl+V)",
            command=self.paste_image,
            width=20
        )
        self.paste_button.pack(side=tk.LEFT, padx=(0, 8))

        # 选择文件按钮
        self.select_button = ttk.Button(
            button_row2,
            text="📁 选择文件",
            command=self.select_image,
            width=12
        )
        self.select_button.pack(side=tk.LEFT)

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.root,
            variable=self.progress_var,
            mode='indeterminate'
        )

        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        self.status_bar = ttk.Label(
            self.root,
            textvariable=self.status_var,
            relief=tk.SUNKEN,
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def setup_drag_drop(self):
        """设置拖拽功能"""
        self.drop_area.drop_target_register(DND_FILES)
        self.drop_area.dnd_bind('<<Drop>>', self.on_drop)
    
    def on_drop(self, event):
        """处理拖拽事件"""
        files = event.data.split()
        if files:
            file_path = files[0].strip('{}')  # 移除可能的大括号
            self.process_dropped_file(file_path)
    
    def select_image(self, event=None):
        """选择图片文件"""
        # 忽略event参数，仅用于绑定事件
        _ = event

        # 检查OCR是否已初始化
        if not self.ocr_ready:
            messagebox.showwarning("提示", "OCR引擎还未初始化完成，请稍候...")
            return

        # 如果正在处理，不允许选择新图片
        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，请稍候...")
            return

        from tkinter import filedialog
        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=[
                ("图片文件", "*.jpg *.jpeg *.png *.bmp *.gif *.tiff"),
                ("所有文件", "*.*")
            ]
        )
        if file_path:
            self.process_dropped_file(file_path)
    
    def process_dropped_file(self, file_path):
        """处理选择或拖拽的文件"""
        # 检查OCR是否已初始化
        if not self.ocr_ready:
            messagebox.showwarning("提示", "OCR引擎还未初始化完成，请稍候...")
            return

        # 如果正在处理，不允许处理新文件
        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，请稍候...")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("错误", "文件不存在")
            return

        # 检查文件类型
        valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff'}
        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext not in valid_extensions:
            messagebox.showerror("错误", "不支持的文件格式")
            return

        self.current_image_path = file_path
        self.display_image(file_path)
        self.start_ocr_processing(file_path)
    
    def display_image(self, image_path):
        """显示图片预览"""
        try:
            # 打开图片并保存原始图片对象
            self.original_image = Image.open(image_path)
            
            # 重置缩放比例
            self.current_scale = 1.0
            
            # 适中显示图片
            self.fit_image()
            
        except Exception as e:
            messagebox.showerror("错误", f"图片显示失败: {e}")
            self.original_image = None
            self.current_scale = 1.0

    def start_ocr_processing(self, image_path):
        """开始OCR处理（异步）"""
        # 设置处理状态
        self.is_processing = True
        self.status_var.set("正在识别文字...")
        self.copy_button.configure(state=tk.DISABLED)
        self.clear_button.configure(state=tk.DISABLED)

        # 显示进度条
        self.progress_bar.pack(side=tk.BOTTOM, fill=tk.X, before=self.status_bar)
        self.progress_bar.start(10)  # 开始动画

        # 清空之前的结果
        self.text_area.delete(1.0, tk.END)
        self.text_area.insert(1.0, "正在识别中，请稍候...")

        # 在新线程中处理OCR
        thread = threading.Thread(
            target=self.ocr_worker,
            args=(image_path,),
            daemon=True
        )
        thread.start()
    
    def ocr_worker(self, image_path):
        """OCR工作线程"""
        try:
            result_text = self.ocr_processor.process_image(image_path)
            # 在主线程中更新UI
            self.root.after(0, self.update_results, result_text, None)
        except Exception as e:
            self.root.after(0, self.update_results, None, str(e))
    
    def update_results(self, text, error):
        """更新识别结果"""
        # 停止进度条并隐藏
        self.progress_bar.stop()
        self.progress_bar.pack_forget()

        # 重置处理状态
        self.is_processing = False
        self.clear_button.configure(state=tk.NORMAL)

        if error:
            self.status_var.set(f"识别失败: {error}")
            self.text_area.delete(1.0, tk.END)
            self.text_area.insert(1.0, f"识别失败: {error}")
            messagebox.showerror("错误", f"OCR处理失败: {error}")
        else:
            self.text_area.delete(1.0, tk.END)
            self.text_area.insert(1.0, text)
            self.copy_button.configure(state=tk.NORMAL)
            self.status_var.set("识别完成")
    
    def copy_text(self):
        """复制文本到剪贴板"""
        text = self.text_area.get(1.0, tk.END).strip()
        if text:
            pyperclip.copy(text)
            self.status_var.set("文本已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "没有可复制的文本")
    
    def clear_results(self):
        """清空结果"""
        # 如果正在处理，不允许清空
        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，无法清空")
            return

        self.text_area.delete(1.0, tk.END)
        self.drop_area.configure(
            image="",
            text="📷 拖拽图片到此处\n📋 或按Ctrl+V粘贴\n🖱️ 或点击选择图片\n🔍 使用鼠标滚轮缩放"
        )
        self.drop_area.image = None
        self.copy_button.configure(state=tk.DISABLED)
        self.current_image_path = None
        self.status_var.set("就绪")

    def paste_image(self):
        """从剪贴板粘贴图片"""
        # 检查OCR是否已初始化
        if not self.ocr_ready:
            messagebox.showwarning("提示", "OCR引擎还未初始化完成，请稍候...")
            return

        # 如果正在处理，不允许粘贴
        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，请稍候...")
            return

        try:
            # 从剪贴板获取数据
            clipboard_data = ImageGrab.grabclipboard()

            if clipboard_data is None:
                messagebox.showwarning("提示", "剪贴板中没有图片数据")
                return

            # 检查剪贴板数据类型
            if isinstance(clipboard_data, list):
                # 如果是文件路径列表，尝试处理第一个图片文件
                if clipboard_data and len(clipboard_data) > 0:
                    file_path = clipboard_data[0]
                    if os.path.exists(file_path):
                        # 检查是否为图片文件
                        valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff'}
                        file_ext = os.path.splitext(file_path)[1].lower()
                        if file_ext in valid_extensions:
                            self.process_dropped_file(file_path)
                            return
                        else:
                            messagebox.showwarning("提示", "剪贴板中的文件不是支持的图片格式")
                            return
                    else:
                        messagebox.showwarning("提示", "剪贴板中的文件路径无效")
                        return
                else:
                    messagebox.showwarning("提示", "剪贴板中没有有效的文件数据")
                    return

            # 检查是否为PIL图片对象
            if not hasattr(clipboard_data, 'save'):
                messagebox.showwarning("提示", "剪贴板中的数据不是有效的图片格式")
                return

            # 保存临时文件
            temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            temp_path = temp_file.name
            temp_file.close()

            # 保存图片到临时文件
            clipboard_data.save(temp_path, 'PNG')

            # 处理图片
            self.current_image_path = temp_path
            self.display_image(temp_path)
            self.start_ocr_processing(temp_path)

            self.status_var.set("已粘贴图片，开始识别...")

        except Exception as e:
            messagebox.showerror("错误", f"粘贴图片失败: {e}")

    def show_help(self):
        """显示使用说明"""
        help_text = """
OCR文字识别工具 - 使用说明

📷 图片输入方式：
• 拖拽：直接将图片文件拖拽到左侧区域
• 粘贴：按Ctrl+V粘贴剪贴板中的图片
• 选择：点击左侧区域或按Ctrl+O选择文件

🖱️ 图片操作：
• 鼠标滚轮：放大/缩小图片
• 右键菜单：
  - 适中显示：自动调整图片大小以适应显示区域
  - 重置缩放：恢复原始大小

⌨️ 快捷键：
• Ctrl+V：粘贴图片
• Ctrl+O：选择图片文件
• Ctrl+C：复制识别结果
• Ctrl+R：清空结果
• Ctrl+Q：退出程序

📝 支持格式：
• 图片格式：JPG, PNG, BMP, GIF, TIFF
• 文字语言：中文、英文等多种语言

💡 使用技巧：
• 图片清晰度越高，识别效果越好
• 文字对比度高的图片识别更准确
• 支持截图工具直接粘贴
• 首次运行需要下载模型，请耐心等待
        """

        help_window = tk.Toplevel(self.root)
        help_window.title("使用说明")
        help_window.geometry("500x600")
        help_window.resizable(False, False)

        # 居中显示
        help_window.transient(self.root)
        help_window.grab_set()

        text_widget = scrolledtext.ScrolledText(
            help_window,
            wrap=tk.WORD,
            font=("Microsoft YaHei", 11),
            padx=20,
            pady=20
        )
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(1.0, help_text)
        text_widget.configure(state=tk.DISABLED)

        # 关闭按钮
        close_btn = ttk.Button(
            help_window,
            text="关闭",
            command=help_window.destroy
        )
        close_btn.pack(pady=10)

    def show_about(self):
        """显示关于对话框"""
        about_text = """
OCR文字识别工具 v1.0

基于PaddleOCR开发的图片文字识别工具

✨ 主要特性：
• 支持多种图片输入方式
• 高精度文字识别
• 简洁易用的界面
• 丰富的快捷键支持

🔧 技术栈：
• PaddleOCR - 文字识别引擎
• Tkinter - 用户界面
• PIL - 图片处理

📧 如有问题或建议，欢迎反馈！
        """

        messagebox.showinfo("关于", about_text)

    def run(self):
        """运行应用程序"""
        self.root.mainloop()

    def on_mouse_wheel(self, event):
        """处理鼠标滚轮事件"""
        if not self.original_image:
            return

        # 根据滚轮方向确定缩放方向
        if event.delta > 0:  # 向上滚动，放大
            new_scale = min(self.current_scale + self.scale_step, self.max_scale)
        else:  # 向下滚动，缩小
            new_scale = max(self.current_scale - self.scale_step, self.min_scale)

        if new_scale != self.current_scale:
            self.current_scale = new_scale
            self.zoom_image()

    def zoom_image(self):
        """根据当前缩放比例缩放图片"""
        if not self.original_image:
            return

        # 计算新的尺寸
        new_width = int(self.original_image.size[0] * self.current_scale)
        new_height = int(self.original_image.size[1] * self.current_scale)

        # 缩放图片
        resized_image = self.original_image.resize(
            (new_width, new_height),
            Image.Resampling.LANCZOS
        )

        # 更新显示
        photo = ImageTk.PhotoImage(resized_image)
        self.drop_area.configure(image=photo)
        self.drop_area.image = photo

    def fit_image(self):
        """适中显示图片"""
        if not self.original_image:
            return

        # 获取显示区域的大小
        display_width = self.drop_area.winfo_width()
        display_height = self.drop_area.winfo_height()

        if display_width <= 1 or display_height <= 1:
            # 如果显示区域尚未准备好，延迟执行
            self.root.after(100, self.fit_image)
            return

        # 计算缩放比例
        width_ratio = display_width / self.original_image.size[0]
        height_ratio = display_height / self.original_image.size[1]
        
        # 使用较小的比例，确保图片完全显示在区域内
        self.current_scale = min(width_ratio, height_ratio) * 0.9  # 留出一些边距
        self.zoom_image()

    def set_scale(self, scale):
        """设置指定的缩放比例"""
        if self.original_image:
            self.current_scale = scale
            self.zoom_image()

    def show_context_menu(self, event):
        """显示右键菜单"""
        if self.original_image:  # 只在有图片时显示菜单
            self.context_menu.post(event.x_root, event.y_root)

    def show_settings(self):
        """显示设置对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("OCR设置")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 居中显示
        dialog.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() - dialog.winfo_width()) // 2
        y = self.root.winfo_y() + (self.root.winfo_height() - dialog.winfo_height()) // 2
        dialog.geometry(f"+{x}+{y}")
        
        # 创建设置框架
        frame = ttk.Frame(dialog, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # GPU加速
        gpu_var = tk.BooleanVar(value=self.ocr_processor.config["use_gpu"])
        ttk.Checkbutton(
            frame,
            text="使用GPU加速（需要CUDA支持）",
            variable=gpu_var
        ).pack(anchor=tk.W, pady=5)
        
        # 语言选择
        ttk.Label(frame, text="识别语言：").pack(anchor=tk.W, pady=(10,5))
        lang_var = tk.StringVar(value=self.ocr_processor.config["language"])
        lang_combo = ttk.Combobox(
            frame,
            textvariable=lang_var,
            values=["ch", "en", "french", "german", "korean", "japan"],
            state="readonly"
        )
        lang_combo.pack(fill=tk.X, pady=5)
        
        # 模型信息
        ttk.Separator(frame).pack(fill=tk.X, pady=15)
        ttk.Label(frame, text="模型信息：").pack(anchor=tk.W, pady=5)
        
        info_text = f"模型目录：{self.ocr_processor.models_dir}\n"
        if self.ocr_processor.check_model_files():
            info_text += "状态：已安装"
        else:
            info_text += "状态：未安装"
        
        ttk.Label(
            frame,
            text=info_text,
            wraplength=350
        ).pack(anchor=tk.W, pady=5)
        
        # 按钮框架
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=tk.X, pady=20)
        
        def save_settings():
            config = self.ocr_processor.config.copy()
            config["use_gpu"] = gpu_var.get()
            config["language"] = lang_var.get()
            self.ocr_processor.save_config(config)
            
            messagebox.showinfo(
                "提示",
                "设置已保存。\n需要重启程序才能生效。"
            )
            dialog.destroy()
        
        ttk.Button(
            button_frame,
            text="保存",
            command=save_settings
        ).pack(side=tk.RIGHT, padx=5)
        
        ttk.Button(
            button_frame,
            text="取消",
            command=dialog.destroy
        ).pack(side=tk.RIGHT)

    def redownload_model(self):
        """重新下载模型"""
        if messagebox.askyesno(
            "确认",
            "确定要重新下载模型文件吗？\n这将删除现有的模型文件。"
        ):
            # 删除现有模型文件
            if os.path.exists(self.ocr_processor.models_dir):
                shutil.rmtree(self.ocr_processor.models_dir)
            os.makedirs(self.ocr_processor.models_dir)
            
            # 显示下载对话框
            self.show_model_download_dialog()


if __name__ == "__main__":
    try:
        app = OCRApp()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        input("按回车键退出...")
